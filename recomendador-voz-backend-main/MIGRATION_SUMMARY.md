# Migración de Google Vertex AI a Servidor Personalizado

## Resumen de Cambios

Se ha migrado exitosamente el proyecto del uso de Google Vertex AI (Gemini) a tu servidor personalizado de IA.

## Archivos Modificados

### 1. `modules/AIService.js`
- **Antes**: Usaba `@google-cloud/vertexai` con Gemini 2.0 Flash
- **Después**: Usa `axios` para hacer peticiones HTTP a tu servidor personalizado
- **Cambios principales**:
  - Reemplazado cliente de Vertex AI por cliente HTTP con axios
  - Implementado sistema de retry automático
  - Adaptado formato de payload para tu API
  - Manejo de respuestas según el formato de tu servidor

### 2. `.env`
- **Agregadas nuevas variables**:
  ```
  IA_API_URL="https://dev.dl2discovery.org/llm-api/v1/"
  IA_API_KEY="9dcd0147-11e2-4e9e-aaf3-05e1498ce828"
  IA_PRESETID_GENCHARBOT="mapp-gen-char-bot"
  IA_PRESETID_IA_VS_PLAYER="mapp-Claude_enygma_V2"
  ```

### 3. `package.json`
- **Removida dependencia**: `@google-cloud/vertexai`
- **Mantenida dependencia**: `axios` (ya existía)
- **Mantenida dependencia**: `@google-cloud/speech` (para speech-to-text)

## Configuración de la API

### Formato del Payload
```json
{
  "id": {
    "ses": "session-id",
    "clt": "aura-voice-client",
    "corr": "correlation-id"
  },
  "preset": "mapp-Claude_enygma_V2",
  "query": "texto del usuario",
  "prompt_params": {
    "preamble": "prompt del sistema"
  },
  "model_params": {
    "max_tokens": 500
  }
}
```

### Formato de Respuesta Esperado
```json
{
  "ok": true,
  "output": "respuesta de la IA",
  "id": {
    "ses": "session-id",
    "corr": "correlation-id"
  },
  "sizes": {
    "input": 1539,
    "output": 66
  }
}
```

## Funcionalidades Implementadas

1. **Autenticación**: Bearer token en headers
2. **Retry automático**: 3 intentos con backoff exponencial
3. **Manejo de contexto**: Incluye historial de conversación
4. **Logging**: Logs informativos para debugging
5. **Manejo de errores**: Respuesta de fallback en caso de error

## Presets Disponibles

- `mapp-gen-char-bot`: Para generación de personajes
- `mapp-Claude_enygma_V2`: Para conversación IA vs jugador
- `mapp-Test akinator_claude37Bi`: Para conversación jugador vs IA

## Pruebas Realizadas

✅ Conexión exitosa al servidor personalizado
✅ Envío de payload correcto
✅ Recepción y procesamiento de respuestas
✅ Integración con el servidor principal
✅ Mantenimiento del prompt original de Aura

## Próximos Pasos

El sistema está listo para usar. Puedes:

1. Iniciar el servidor con `npm run dev`
2. El sistema automáticamente usará tu servidor personalizado
3. Todas las funcionalidades de voz y recomendaciones funcionan igual que antes
4. Si necesitas cambiar presets o configuración, modifica las variables en `.env`

## Notas Importantes

- El sistema mantiene toda la funcionalidad original
- El prompt de Aura se conserva exactamente igual
- La integración es transparente para el frontend
- Se mantiene el sistema de speech-to-text de Google Cloud
- Solo se cambió el servicio de generación de texto de IA
