const axios = require('axios')
const config = require('./config')

// Configuración de la API
const API_CONFIG = {
  baseURL: process.env.IA_API_URL || 'https://dev.dl2discovery.org/llm-api/v1/',
  apiKey: process.env.IA_API_KEY || '9dcd0147-11e2-4e9e-aaf3-05e1498ce828',
  presets: {
    iaVsPlayer: process.env.IA_PREIA_PRESETID_IA_VS_PLAYERSETID_IA_VS_PLAYER || 'mapp-Claude_enygma_V2',
    genCharBot: process.env.IA_PRESETID_GENCHARBOT || 'mapp-gen-char-bot'
  }
}

// Cliente HTTP configurado
let httpClient

const createAIService = () => {
  httpClient = axios.create({
    baseURL: API_CONFIG.baseURL,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${API_CONFIG.apiKey}`
    },
    timeout: 30000 // 30 segundos
  })

  console.log('✅ AI Service initialized with custom server:', API_CONFIG.baseURL)
}

/**
 * Construye el payload para la API
 */
const buildPayload = (query, conversationHistory, sessionId = null) => {
  console.log('🔍 buildPayload input - query:', query)
  console.log('🔍 buildPayload input - conversationHistory length:', conversationHistory?.length || 0)

  // Construir el contexto de conversación como parte del query
  let fullQuery = query

  if (conversationHistory && conversationHistory.length > 1) {
    // Incluir las últimas 5 interacciones para mantener contexto
    const recentHistory = conversationHistory.slice(-5)
    const contextParts = recentHistory.slice(0, -1).map(turn =>
      `${turn.user ? 'Usuario' : 'Asistente'}: ${turn.content}`
    ).join('\n')

    if (contextParts) {
      fullQuery = `Contexto de conversación anterior:\n${contextParts}\n\nPregunta actual: ${query}`
    }
  }

  console.log('🔍 buildPayload output - fullQuery:', fullQuery)

  return {
    id: {
      ses: sessionId || `session-${Date.now()}`,
      clt: 'enygma-game-client',
      corr: `conv-${Date.now()}`
    },
    preset: API_CONFIG.presets.iaVsPlayer,
    query: fullQuery
  }
}

/**
 * Realiza una petición a la API con retry
 */
const makeRequest = async (payload, retries = 3) => {
  let lastError = null

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      console.log('📤 Sending payload:', JSON.stringify(payload, null, 2)) // AÑADIR ESTE LOG

      const response = await httpClient.post('generate', payload)

      console.log('📥 Raw response:', JSON.stringify(response.data, null, 2)) // AÑADIR ESTE LOG

      if (response.data && response.data.output) {
        console.log(`✅ AI response received successfully (attempt ${attempt})`)
        return {
          success: true,
          data: response.data,
          attempts: attempt
        }
      } else {
        console.log('❌ Response structure:', Object.keys(response.data || {})) // AÑADIR ESTE LOG
        throw new Error('Invalid response format from AI server')
      }
    } catch (error) {
      lastError = error
      console.warn(`⚠️ AI Service attempt ${attempt} failed:`, error.message)

      if (error.response && error.response.data) {
        console.warn('📥 Error response:', JSON.stringify(error.response.data, null, 2)) // MEJORAR ESTE LOG
      }

      if (attempt < retries) {
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }
  }

  return {
    success: false,
    error: lastError?.message || 'All retry attempts failed',
    attempts: retries
  }
}

/**
 * Genera respuesta de IA usando el servidor personalizado
 */
const generateAIReply = async (conversationHistory) => {
  try {
    if (!httpClient) {
      throw new Error('AI Service not initialized. Call createAIService() first.')
    }

    // Obtener la última pregunta del usuario
    const lastUserMessage = conversationHistory[conversationHistory.length - 1]
    if (!lastUserMessage || !lastUserMessage.user) {
      throw new Error('No user message found in conversation history')
    }

    const query = lastUserMessage.content
    const sessionId = `voice-session-${Date.now()}`

    // Construir payload
    const payload = buildPayload(query, conversationHistory, sessionId)

    // Realizar petición
    const result = await makeRequest(payload)

    if (!result.success) {
      throw new Error(result.error || 'Failed to get AI response')
    }

    // Extraer y limpiar la respuesta
    let aiResponse = result.data.output.trim()

    // Log para debugging
    console.log('🤖 AI Response received:', aiResponse.substring(0, 100) + '...')

    return aiResponse

  } catch (error) {
    console.error('❌ Error generating AI reply:', error)

    // Respuesta de fallback
    return '[NONE]'
  }
}

module.exports = {
  createAIService,
  generateAIReply
}
